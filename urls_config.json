{"website_groups": {"rakuten_docs": {"description": "Rakuten internal documentation", "urls": ["https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/pipeline-service-page"]}, "test_sites": {"description": "Test websites for scraping", "urls": ["https://httpbin.org/html", "https://quotes.toscrape.com/", "https://books.toscrape.com/"]}, "documentation_sites": {"description": "Various documentation websites", "urls": ["https://docs.python.org/3/", "https://requests.readthedocs.io/en/latest/", "https://beautiful-soup-4.readthedocs.io/en/latest/"]}, "custom_sites": {"description": "Add your custom URLs here", "urls": ["https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/pipeline-service-page", "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/paas-getting-started", "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/pipeline-settings-page", "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/create-pipeline", "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/create-pipeline-sql-task"]}}, "scraping_settings": {"output_format": "structured", "delay_between_requests": 1, "timeout": 30, "generate_pdf": true, "save_individual_json": true}}