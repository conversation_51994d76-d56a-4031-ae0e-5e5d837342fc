{"url": "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/pipeline-service-page", "status_code": 200, "title": "Pipeline Service | One Cloud", "scraped_at": "2025-06-30 11:46:04", "content": {"headings": [{"level": 1, "text": "Pipeline Service", "id": ""}], "paragraphs": ["The Pipeline Service is a comprehensive and intuitive platform that empowers users to effortlessly build, schedule, and monitor complex pipelines. It enables users to concentrate solely on their pipeline business logic, while efficiently managing essential functionalities such as scheduling, notifications, and dependency management in the background"], "links": [{"text": "Skip to main content", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/pipeline-service-page#__docusaurus_skipToContent_fallback", "original_href": "#__docus<PERSON>_skipToContent_fallback"}, {"text": "", "url": "https://onecloud.rakuten-it.com/one-docs/", "original_href": "/one-docs/"}, {"text": "Overview", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/pipeline-service-page", "original_href": "#"}, {"text": "Overview", "url": "https://onecloud.rakuten-it.com/one-docs/docs/roc-overview", "original_href": "/one-docs/docs/roc-overview"}, {"text": "Terms of Service", "url": "https://onecloud.rakuten-it.com/one-docs/docs/roc-tos/roc-tos-home", "original_href": "/one-docs/docs/roc-tos/roc-tos-home"}, {"text": "Security", "url": "https://onecloud.rakuten-it.com/one-docs/docs/category/one-cloud-security", "original_href": "/one-docs/docs/category/one-cloud-security"}, {"text": "Getting Started", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Portal-guide/qstart", "original_href": "/one-docs/docs/Portal-guide/qstart"}, {"text": "Products", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Authorization/security-guide-iam", "original_href": "/one-docs/docs/Authorization/security-guide-iam"}, {"text": "Billing", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/pipeline-service-page", "original_href": "#"}, {"text": "Free Tier Offer", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Billing/free-tier-offer", "original_href": "/one-docs/docs/Billing/free-tier-offer"}, {"text": "Quickstart Guide", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Horizon Billing/HorizonBillingQuickstartGuide", "original_href": "/one-docs/docs/Horizon Billing/HorizonBillingQuickstartGuide"}, {"text": "User Manual", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Horizon Billing/HorizonBillingUserManual", "original_href": "/one-docs/docs/Horizon Billing/HorizonBillingUserManual"}, {"text": "Pricing", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Billing/pricing-overview", "original_href": "/one-docs/docs/Billing/pricing-overview"}, {"text": "Resources", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/pipeline-service-page", "original_href": "#"}, {"text": "Release Notes", "url": "https://onecloud.rakuten-it.com/one-docs/docs/category/whats-new", "original_href": "/one-docs/docs/category/whats-new"}, {"text": "One Cloud Community Meetup", "url": "https://confluence.rakuten-it.com/confluence/display/GTSD/One+Cloud+Meetup", "original_href": "https://confluence.rakuten-it.com/confluence/display/GTSD/One+Cloud+Meetup"}, {"text": "Video Library", "url": "https://onecloud.rakuten-it.com/one-docs/docs/videos/video-library", "original_href": "/one-docs/docs/videos/video-library"}, {"text": "How to contribute docs", "url": "https://onecloud.rakuten-it.com/one-docs/docs/onboarding/how-to-contribute", "original_href": "/one-docs/docs/onboarding/how-to-contribute"}, {"text": "Contact Us", "url": "https://onecloud.rakuten-it.com/tam-support", "original_href": "https://onecloud.rakuten-it.com/tam-support"}, {"text": "Support", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/pipeline-service-page", "original_href": "#"}, {"text": "User Support", "url": "https://onecloud.rakuten-it.com/one-docs/docs/User Support/cpd-support", "original_href": "/one-docs/docs/User Support/cpd-support"}, {"text": "CCoE", "url": "https://onecloud.rakuten-it.com/one-docs/docs/User Support/Cloud Center of Excellence", "original_href": "/one-docs/docs/User Support/Cloud Center of Excellence"}, {"text": "FAQs", "url": "https://onecloud.rakuten-it.com/one-docs/docs/category/faqs", "original_href": "/one-docs/docs/category/faqs"}, {"text": "Troubleshooting", "url": "https://onecloud.rakuten-it.com/one-docs/docs/category/troubleshooting", "original_href": "/one-docs/docs/category/troubleshooting"}, {"text": "Subscribing to Mailing List", "url": "https://onecloud.rakuten-it.com/one-docs/docs/roc-overview#subscribing-to-one-cloud-mailing-list", "original_href": "/one-docs/docs/roc-overview#subscribing-to-one-cloud-mailing-list"}, {"text": "One Cloud Portal", "url": "https://onecloud.rakuten-it.com/", "original_href": "https://onecloud.rakuten-it.com/"}, {"text": "Authorization", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Authorization/security-guide-iam", "original_href": "/one-docs/docs/Authorization/security-guide-iam"}, {"text": "Common Architecture Platform", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Common Architecture Platform/rapid-overview", "original_href": "/one-docs/docs/Common Architecture Platform/rapid-overview"}, {"text": "Compute", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Compute/compute-home", "original_href": "/one-docs/docs/Compute/compute-home"}, {"text": "Content Management", "url": "https://onecloud.rakuten-it.com/one-docs/docs/category/content-management", "original_href": "/one-docs/docs/category/content-management"}, {"text": "Customer Support Solutions", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Customer Support Solutions/customer-support-solutions-home", "original_href": "/one-docs/docs/Customer Support Solutions/customer-support-solutions-home"}, {"text": "Customer Understanding and Profiling", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Customer Understanding and Profiling/customer-understatnding-and-profiling-home", "original_href": "/one-docs/docs/Customer Understanding and Profiling/customer-understatnding-and-profiling-home"}, {"text": "Data Analytics Platform", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data Analytics Platform/data-analytics-platform-home", "original_href": "/one-docs/docs/Data Analytics Platform/data-analytics-platform-home"}, {"text": "Database", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Database/database-home", "original_href": "/one-docs/docs/Database/database-home"}, {"text": "Data and AI Services", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data and AI Services/data-and-ai-services-home", "original_href": "/one-docs/docs/Data and AI Services/data-and-ai-services-home"}, {"text": "DevOps/SRE", "url": "https://onecloud.rakuten-it.com/one-docs/docs/DevOps-SRE/devops-sre-home", "original_href": "/one-docs/docs/DevOps-SRE/devops-sre-home"}, {"text": "CICD", "url": "https://onecloud.rakuten-it.com/one-docs/docs/CICD/cicd-home", "original_href": "/one-docs/docs/CICD/cicd-home"}, {"text": "Facility", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Facility/facility-home", "original_href": "/one-docs/docs/Facility/facility-home"}, {"text": "Fraud Prevention Platform", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Fraud Prevention Platform/fraud-prevention-platform-home", "original_href": "/one-docs/docs/Fraud Prevention Platform/fraud-prevention-platform-home"}, {"text": "Integration Service", "url": "https://onecloud.rakuten-it.com/one-docs/docs/category/integration-service", "original_href": "/one-docs/docs/category/integration-service"}, {"text": "Loyalty Platform", "url": "https://onecloud.rakuten-it.com/one-docs/docs/category/loyalty-platform", "original_href": "/one-docs/docs/category/loyalty-platform"}, {"text": "Marketing and Personalization", "url": "https://onecloud.rakuten-it.com/one-docs/docs/category/marketing-and-personalization", "original_href": "/one-docs/docs/category/marketing-and-personalization"}, {"text": "Member data", "url": "https://onecloud.rakuten-it.com/one-docs/docs/category/member-data", "original_href": "/one-docs/docs/category/member-data"}, {"text": "Membership Platform", "url": "https://onecloud.rakuten-it.com/one-docs/docs/category/membership-platform", "original_href": "/one-docs/docs/category/membership-platform"}, {"text": "Monitoring", "url": "https://onecloud.rakuten-it.com/one-docs/docs/category/monitoring", "original_href": "/one-docs/docs/category/monitoring"}, {"text": "Network", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Network/network-home", "original_href": "/one-docs/docs/Network/network-home"}, {"text": "Others", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Others/others-home", "original_href": "/one-docs/docs/Others/others-home"}, {"text": "Payment Platform", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Payment Platform/payment-platform", "original_href": "/one-docs/docs/Payment Platform/payment-platform"}, {"text": "Data", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data/data-guide", "original_href": "/one-docs/docs/Data/data-guide"}, {"text": "Pipeline Service", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/pipeline-service-page", "original_href": "/one-docs/docs/Data/PipelineService/pipeline-service-page"}, {"text": "Getting Started", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/paas-getting-started", "original_href": "/one-docs/docs/Data/PipelineService/paas-getting-started"}, {"text": "Pipeline Settings", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/pipeline-settings-page", "original_href": "/one-docs/docs/Data/PipelineService/pipeline-settings-page"}, {"text": "Create Pipeline", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/create-pipeline", "original_href": "/one-docs/docs/Data/PipelineService/create-pipeline"}, {"text": "Pipeline API", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/pipeline-api", "original_href": "/one-docs/docs/Data/PipelineService/pipeline-api"}, {"text": "Create Job", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/Jobs/create-job", "original_href": "/one-docs/docs/Data/PipelineService/Jobs/create-job"}, {"text": "Create Job Example", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/Jobs/create-job-example", "original_href": "/one-docs/docs/Data/PipelineService/Jobs/create-job-example"}, {"text": "Create External Pipeline", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/external-pipeline", "original_href": "/one-docs/docs/Data/PipelineService/external-pipeline"}, {"text": "<PERSON><PERSON>", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/audit-logs", "original_href": "/one-docs/docs/Data/PipelineService/audit-logs"}, {"text": "Approval Flow", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/pipeline-approval-flow", "original_href": "/one-docs/docs/Data/PipelineService/pipeline-approval-flow"}, {"text": "Pipeline Dependency", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/dependency-management", "original_href": "/one-docs/docs/Data/PipelineService/dependency-management"}, {"text": "Examples", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/Examples/pipeline-example", "original_href": "/one-docs/docs/Data/PipelineService/Examples/pipeline-example"}, {"text": "Registry", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Registry-aaS/registry-as-a-service-home", "original_href": "/one-docs/docs/Registry-aaS/registry-as-a-service-home"}, {"text": "Search Platform", "url": "https://onecloud.rakuten-it.com/one-docs/docs/category/search-platform", "original_href": "/one-docs/docs/category/search-platform"}, {"text": "Security", "url": "https://onecloud.rakuten-it.com/one-docs/docs/category/security", "original_href": "/one-docs/docs/category/security"}, {"text": "Storage", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Storage/storage-home", "original_href": "/one-docs/docs/Storage/storage-home"}, {"text": "", "url": "https://onecloud.rakuten-it.com/one-docs/", "original_href": "/one-docs/"}, {"text": "Data", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data/data-guide", "original_href": "/one-docs/docs/Data/data-guide"}, {"text": "Edit this page", "url": "https://git.rakuten-it.com/projects/TDOC/repos/docusaurus/browse/website/docs/Data/PipelineService/pipeline-service-page.md", "original_href": "https://git.rakuten-it.com/projects/TDOC/repos/docusaurus/browse/website/docs/Data/PipelineService/pipeline-service-page.md"}, {"text": "PreviousData", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data/data-guide", "original_href": "/one-docs/docs/Data/data-guide"}, {"text": "NextGetting Started", "url": "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/paas-getting-started", "original_href": "/one-docs/docs/Data/PipelineService/paas-getting-started"}, {"text": "Whats New", "url": "https://onecloud.rakuten-it.com/one-docs/docs/category/whats-new", "original_href": "/one-docs/docs/category/whats-new"}, {"text": "One Cloud Portal", "url": "https://onecloud.rakuten-it.com", "original_href": "https://onecloud.rakuten-it.com"}, {"text": "Meetup Information", "url": "https://confluence.rakuten-it.com/confluence/display/GTSD/One+Cloud+Meetup", "original_href": "https://confluence.rakuten-it.com/confluence/display/GTSD/One+Cloud+Meetup"}, {"text": "Meetup QAs", "url": "https://confluence.rakuten-it.com/confluence/display/GTSD/Questions+and+Answers+in+One+Cloud+Meetup", "original_href": "https://confluence.rakuten-it.com/confluence/display/GTSD/Questions+and+Answers+in+One+Cloud+Meetup"}, {"text": "Video Channel", "url": "https://officerakuten.sharepoint.com/:f:/s/CPED/EnOsVzsddq5CnhlbDR4qicgBGya8EkQsg0zcH8ByI6bB9g?e=1aOXbZ", "original_href": "https://officerakuten.sharepoint.com/:f:/s/CPED/EnOsVzsddq5CnhlbDR4qicgBGya8EkQsg0zcH8ByI6bB9g?e=1aOXbZ"}, {"text": "Contribute to Documentation", "url": "https://onecloud.rakuten-it.com/one-docs/docs/onboarding/how-to-contribute", "original_href": "/one-docs/docs/onboarding/how-to-contribute"}, {"text": "Contact Us", "url": "https://onecloud.rakuten-it.com/tam-support", "original_href": "https://onecloud.rakuten-it.com/tam-support"}], "images": [{"src": "https://onecloud.rakuten-it.com/one-docs/img/logo_onecloud_blue.svg", "alt": "One Cloud", "original_src": "/one-docs/img/logo_onecloud_blue.svg"}, {"src": "https://onecloud.rakuten-it.com/one-docs/img/logo_onecloud_blue.svg", "alt": "One Cloud", "original_src": "/one-docs/img/logo_onecloud_blue.svg"}, {"src": "https://onecloud.rakuten-it.com/one-docs/img/logo_onecloud_blue.svg", "alt": "One Cloud", "original_src": "/one-docs/img/logo_onecloud_blue.svg"}, {"src": "https://onecloud.rakuten-it.com/one-docs/img/logo_onecloud_blue.svg", "alt": "One Cloud", "original_src": "/one-docs/img/logo_onecloud_blue.svg"}], "tables": [], "lists": [{"type": "ul", "items": ["Overview", "Terms of Service", "Security", "Getting Started"]}, {"type": "ul", "items": ["Free Tier Offer", "Quickstart Guide", "User Manual", "Pricing"]}, {"type": "ul", "items": ["Release Notes", "One Cloud Community Meetup", "Video Library", "How to contribute docs"]}, {"type": "ul", "items": ["User Support", "CCoE", "FAQs", "Troubleshooting", "Subscribing to Mailing List"]}, {"type": "ul", "items": ["Authorization", "Common Architecture Platform", "Compute", "Content Management", "Customer Support Solutions", "Customer Understanding and Profiling", "Data Analytics Platform", "Database", "Data and AI Services", "DevOps/SRE", "CICD", "Facility", "Fraud Prevention Platform", "Integration Service", "Loyalty Platform", "Marketing and Personalization", "Member data", "Membership Platform", "Monitoring", "Network", "Others", "Payment Platform", "DataPipeline ServiceGetting StartedPipeline SettingsCreate PipelinePipeline APICreate JobCreate Job ExampleCreate External PipelineAudit LogsApproval FlowPipeline DependencyExamples", "Pipeline ServiceGetting StartedPipeline SettingsCreate PipelinePipeline APICreate JobCreate Job ExampleCreate External PipelineAudit LogsApproval FlowPipeline DependencyExamples", "Getting Started", "Pipeline Settings", "Create Pipeline", "Pipeline API", "Create Job", "Create Job Example", "Create External Pipeline", "<PERSON><PERSON>", "Approval Flow", "Pipeline Dependency", "Examples", "Registry", "Search Platform", "Security", "Storage"]}, {"type": "ul", "items": ["Pipeline ServiceGetting StartedPipeline SettingsCreate PipelinePipeline APICreate JobCreate Job ExampleCreate External PipelineAudit LogsApproval FlowPipeline DependencyExamples", "Getting Started", "Pipeline Settings", "Create Pipeline", "Pipeline API", "Create Job", "Create Job Example", "Create External Pipeline", "<PERSON><PERSON>", "Approval Flow", "Pipeline Dependency", "Examples"]}, {"type": "ul", "items": ["Getting Started", "Pipeline Settings", "Create Pipeline", "Pipeline API", "Create Job", "Create Job Example", "Create External Pipeline", "<PERSON><PERSON>", "Approval Flow", "Pipeline Dependency", "Examples"]}, {"type": "ul", "items": ["Data", "Pipeline Service"]}, {"type": "ul", "items": ["Whats New", "One Cloud Portal", "Meetup Information", "Meetup QAs", "Video Channel"]}, {"type": "ul", "items": ["Contribute to Documentation", "Contact Us"]}]}}