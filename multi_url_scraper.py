#!/usr/bin/env python3
"""
Multi-URL Web Scraper with PDF Generation
Usage example for scraping multiple websites and generating a combined PDF report
"""

from Web import scrape_multiple_websites, create_pdf_report, save_scraped_data
import json

def main():
    """Main function to demonstrate multi-URL scraping"""
    
    # Define your list of URLs to scrape
    urls_to_scrape = [
        "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/pipeline-service-page",
        "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/paas-getting-started",  # Test URL
        "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/pipeline-settings-page",
        "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/create-pipeline",
        "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/create-pipeline-sql-task"
           # Test URL with quotes
        # Add more URLs here as needed:
        # "https://your-website.com/page1",
        # "https://another-site.com/documentation",
        # "https://blog.example.com/article",
    ]
    
    print("🚀 Starting Multi-Website Scraping")
    print(f"📋 Total URLs to scrape: {len(urls_to_scrape)}")
    print("-" * 50)
    
    # Scrape all websites
    all_scraped_data = scrape_multiple_websites(urls_to_scrape, output_format='structured')
    
    # Save combined data as JSON
    combined_filename = "combined_scraped_data.json"
    with open(combined_filename, 'w', encoding='utf-8') as f:
        json.dump(all_scraped_data, f, indent=2, ensure_ascii=False)
    print(f"💾 Combined data saved to: {combined_filename}")
    
    # Generate PDF report
    pdf_filename = create_pdf_report(all_scraped_data, "multi_website_report.pdf")
    
    # Print summary
    successful = [data for data in all_scraped_data if 'error' not in data]
    failed = [data for data in all_scraped_data if 'error' in data]
    
    print("\n" + "="*50)
    print("📊 SCRAPING SUMMARY")
    print("="*50)
    print(f"✅ Successfully scraped: {len(successful)} websites")
    print(f"❌ Failed to scrape: {len(failed)} websites")
    print(f"📄 PDF report: {pdf_filename}")
    print(f"📁 JSON data: {combined_filename}")
    
    if successful:
        print(f"\n✅ SUCCESSFUL SCRAPES:")
        for i, data in enumerate(successful, 1):
            title = data.get('title', 'No title')[:50]
            print(f"   {i}. {title} - {data['url']}")
    
    if failed:
        print(f"\n❌ FAILED SCRAPES:")
        for i, data in enumerate(failed, 1):
            print(f"   {i}. {data['url']}")
            print(f"      Error: {data['error']}")
    
    return all_scraped_data, pdf_filename


def scrape_custom_list(url_list, output_name="custom_report"):
    """
    Scrape a custom list of URLs
    
    Args:
        url_list (list): List of URLs to scrape
        output_name (str): Base name for output files
    
    Returns:
        tuple: (scraped_data, pdf_filename)
    """
    print(f"🎯 Custom scraping: {len(url_list)} URLs")
    
    # Scrape the URLs
    scraped_data = scrape_multiple_websites(url_list, output_format='structured')
    
    # Generate PDF with custom name
    pdf_filename = f"{output_name}.pdf"
    create_pdf_report(scraped_data, pdf_filename)
    
    # Save JSON with custom name
    json_filename = f"{output_name}.json"
    with open(json_filename, 'w', encoding='utf-8') as f:
        json.dump(scraped_data, f, indent=2, ensure_ascii=False)
    
    print(f"📄 Custom PDF: {pdf_filename}")
    print(f"📁 Custom JSON: {json_filename}")
    
    return scraped_data, pdf_filename


if __name__ == "__main__":
    # Run the main scraping function
    scraped_data, pdf_file = main()
    
    print(f"\n🎉 Scraping completed!")
    print(f"📖 Open '{pdf_file}' to view the combined report")
    
    # Example of custom scraping
    print("\n" + "-"*50)
    print("💡 EXAMPLE: Custom URL List Scraping")
    print("-"*50)
    
    # Uncomment and modify this section to scrape your own custom URLs:
    """
    custom_urls = [
        "https://your-site.com/page1",
        "https://your-site.com/page2",
        "https://another-site.com/docs",
    ]
    
    custom_data, custom_pdf = scrape_custom_list(custom_urls, "my_custom_report")
    print(f"Custom scraping completed: {custom_pdf}")
    """
