import requests
from bs4 import BeautifulSoup
import json
import time
from urllib.parse import urljoin, urlparse


def scrape_webpage(url, output_format='text'):
    """
    Scrape content from a webpage and return it in specified format

    Args:
        url (str): The URL to scrape
        output_format (str): Format for output - 'text', 'html', 'json', or 'structured'

    Returns:
        dict: Scraped content with metadata
    """
    try:
        # Add headers to mimic a real browser
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }

        print(f"Scraping URL: {url}")

        # Make the request
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()  # Raise an exception for bad status codes

        print(f"Status Code: {response.status_code}")
        print(f"Content Type: {response.headers.get('content-type', 'Unknown')}")

        # Parse the HTML content
        soup = BeautifulSoup(response.content, 'html.parser')

        # Extract different types of content
        result = {
            'url': url,
            'status_code': response.status_code,
            'title': soup.title.string.strip() if soup.title else 'No title found',
            'scraped_at': time.strftime('%Y-%m-%d %H:%M:%S'),
        }

        if output_format == 'text':
            # Extract clean text content
            result['content'] = soup.get_text(separator='\n', strip=True)

        elif output_format == 'html':
            # Return the raw HTML
            result['content'] = str(soup)

        elif output_format == 'json':
            # Extract structured data
            result['content'] = extract_structured_data(soup)

        elif output_format == 'structured':
            # Extract common elements in a structured way
            result['content'] = {
                'headings': extract_headings(soup),
                'paragraphs': extract_paragraphs(soup),
                'links': extract_links(soup, url),
                'images': extract_images(soup, url),
                'tables': extract_tables(soup),
                'lists': extract_lists(soup)
            }

        return result

    except requests.exceptions.RequestException as e:
        return {
            'url': url,
            'error': f"Request failed: {str(e)}",
            'scraped_at': time.strftime('%Y-%m-%d %H:%M:%S')
        }
    except Exception as e:
        return {
            'url': url,
            'error': f"Scraping failed: {str(e)}",
            'scraped_at': time.strftime('%Y-%m-%d %H:%M:%S')
        }


def extract_headings(soup):
    """Extract all headings (h1-h6) from the page"""
    headings = []
    for i in range(1, 7):
        for heading in soup.find_all(f'h{i}'):
            headings.append({
                'level': i,
                'text': heading.get_text(strip=True),
                'id': heading.get('id', '')
            })
    return headings


def extract_paragraphs(soup):
    """Extract all paragraph text"""
    paragraphs = []
    for p in soup.find_all('p'):
        text = p.get_text(strip=True)
        if text:  # Only include non-empty paragraphs
            paragraphs.append(text)
    return paragraphs


def extract_links(soup, base_url):
    """Extract all links from the page"""
    links = []
    for link in soup.find_all('a', href=True):
        href = link['href']
        text = link.get_text(strip=True)
        # Convert relative URLs to absolute URLs
        absolute_url = urljoin(base_url, href)
        links.append({
            'text': text,
            'url': absolute_url,
            'original_href': href
        })
    return links


def extract_images(soup, base_url):
    """Extract all images from the page"""
    images = []
    for img in soup.find_all('img'):
        src = img.get('src', '')
        alt = img.get('alt', '')
        if src:
            absolute_url = urljoin(base_url, src)
            images.append({
                'src': absolute_url,
                'alt': alt,
                'original_src': src
            })
    return images


def extract_tables(soup):
    """Extract table data"""
    tables = []
    for table in soup.find_all('table'):
        table_data = []
        rows = table.find_all('tr')
        for row in rows:
            cells = row.find_all(['td', 'th'])
            row_data = [cell.get_text(strip=True) for cell in cells]
            if row_data:  # Only include non-empty rows
                table_data.append(row_data)
        if table_data:
            tables.append(table_data)
    return tables


def extract_lists(soup):
    """Extract list items"""
    lists = []
    for list_tag in soup.find_all(['ul', 'ol']):
        list_items = []
        for li in list_tag.find_all('li'):
            text = li.get_text(strip=True)
            if text:
                list_items.append(text)
        if list_items:
            lists.append({
                'type': list_tag.name,
                'items': list_items
            })
    return lists


def extract_structured_data(soup):
    """Extract JSON-LD structured data if available"""
    structured_data = []
    scripts = soup.find_all('script', type='application/ld+json')
    for script in scripts:
        try:
            data = json.loads(script.string)
            structured_data.append(data)
        except json.JSONDecodeError:
            continue
    return structured_data


def save_scraped_data(data, filename=None):
    """Save scraped data to a file"""
    if filename is None:
        # Generate filename based on URL and timestamp
        parsed_url = urlparse(data['url'])
        domain = parsed_url.netloc.replace('.', '_')
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        filename = f"scraped_{domain}_{timestamp}.json"

    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

    print(f"Data saved to: {filename}")
    return filename


# Main execution
if __name__ == "__main__":
    # URL to scrape
    url = "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/pipeline-service-page"

    print("=== Web Scraping Started ===")

    # Scrape in different formats
    formats = ['text', 'structured']

    for format_type in formats:
        print(f"\n--- Scraping in {format_type} format ---")
        scraped_data = scrape_webpage(url, output_format=format_type)

        if 'error' in scraped_data:
            print(f"Error: {scraped_data['error']}")
        else:
            print(f"Title: {scraped_data['title']}")
            print(f"Scraped at: {scraped_data['scraped_at']}")

            # Save the data
            filename = save_scraped_data(scraped_data, f"scraped_data_{format_type}.json")

            # Display a preview of the content
            if format_type == 'text':
                content = scraped_data['content']
                print(f"\nContent preview (first 500 characters):")
                print(content[:500] + "..." if len(content) > 500 else content)

            elif format_type == 'structured':
                content = scraped_data['content']
                print(f"\nStructured data summary:")
                print(f"- Headings: {len(content.get('headings', []))}")
                print(f"- Paragraphs: {len(content.get('paragraphs', []))}")
                print(f"- Links: {len(content.get('links', []))}")
                print(f"- Images: {len(content.get('images', []))}")
                print(f"- Tables: {len(content.get('tables', []))}")
                print(f"- Lists: {len(content.get('lists', []))}")

    print("\n=== Web Scraping Completed ===")
