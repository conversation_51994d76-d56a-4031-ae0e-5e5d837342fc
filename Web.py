import requests
from bs4 import Beautiful<PERSON>oup
import json
import time
from urllib.parse import urljoin, urlparse
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_JUSTIFY
import os
from datetime import datetime


def scrape_webpage(url, output_format='text'):
    """
    Scrape content from a webpage and return it in specified format

    Args:
        url (str): The URL to scrape
        output_format (str): Format for output - 'text', 'html', 'json', or 'structured'

    Returns:
        dict: Scraped content with metadata
    """
    try:
        # Add headers to mimic a real browser
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }

        print(f"Scraping URL: {url}")

        # Make the request
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()  # Raise an exception for bad status codes

        print(f"Status Code: {response.status_code}")
        print(f"Content Type: {response.headers.get('content-type', 'Unknown')}")

        # Parse the HTML content
        soup = BeautifulSoup(response.content, 'html.parser')

        # Extract different types of content
        result = {
            'url': url,
            'status_code': response.status_code,
            'title': soup.title.string.strip() if soup.title else 'No title found',
            'scraped_at': time.strftime('%Y-%m-%d %H:%M:%S'),
        }

        if output_format == 'text':
            # Extract clean text content
            result['content'] = soup.get_text(separator='\n', strip=True)

        elif output_format == 'html':
            # Return the raw HTML
            result['content'] = str(soup)

        elif output_format == 'json':
            # Extract structured data
            result['content'] = extract_structured_data(soup)

        elif output_format == 'structured':
            # Extract common elements in a structured way
            result['content'] = {
                'headings': extract_headings(soup),
                'paragraphs': extract_paragraphs(soup),
                'links': extract_links(soup, url),
                'images': extract_images(soup, url),
                'tables': extract_tables(soup),
                'lists': extract_lists(soup)
            }

        return result

    except requests.exceptions.RequestException as e:
        return {
            'url': url,
            'error': f"Request failed: {str(e)}",
            'scraped_at': time.strftime('%Y-%m-%d %H:%M:%S')
        }
    except Exception as e:
        return {
            'url': url,
            'error': f"Scraping failed: {str(e)}",
            'scraped_at': time.strftime('%Y-%m-%d %H:%M:%S')
        }


def extract_headings(soup):
    """Extract all headings (h1-h6) from the page"""
    headings = []
    for i in range(1, 7):
        for heading in soup.find_all(f'h{i}'):
            headings.append({
                'level': i,
                'text': heading.get_text(strip=True),
                'id': heading.get('id', '')
            })
    return headings


def extract_paragraphs(soup):
    """Extract all paragraph text"""
    paragraphs = []
    for p in soup.find_all('p'):
        text = p.get_text(strip=True)
        if text:  # Only include non-empty paragraphs
            paragraphs.append(text)
    return paragraphs


def extract_links(soup, base_url):
    """Extract all links from the page"""
    links = []
    for link in soup.find_all('a', href=True):
        href = link['href']
        text = link.get_text(strip=True)
        # Convert relative URLs to absolute URLs
        absolute_url = urljoin(base_url, href)
        links.append({
            'text': text,
            'url': absolute_url,
            'original_href': href
        })
    return links


def extract_images(soup, base_url):
    """Extract all images from the page"""
    images = []
    for img in soup.find_all('img'):
        src = img.get('src', '')
        alt = img.get('alt', '')
        if src:
            absolute_url = urljoin(base_url, src)
            images.append({
                'src': absolute_url,
                'alt': alt,
                'original_src': src
            })
    return images


def extract_tables(soup):
    """Extract table data"""
    tables = []
    for table in soup.find_all('table'):
        table_data = []
        rows = table.find_all('tr')
        for row in rows:
            cells = row.find_all(['td', 'th'])
            row_data = [cell.get_text(strip=True) for cell in cells]
            if row_data:  # Only include non-empty rows
                table_data.append(row_data)
        if table_data:
            tables.append(table_data)
    return tables


def extract_lists(soup):
    """Extract list items"""
    lists = []
    for list_tag in soup.find_all(['ul', 'ol']):
        list_items = []
        for li in list_tag.find_all('li'):
            text = li.get_text(strip=True)
            if text:
                list_items.append(text)
        if list_items:
            lists.append({
                'type': list_tag.name,
                'items': list_items
            })
    return lists


def extract_structured_data(soup):
    """Extract JSON-LD structured data if available"""
    structured_data = []
    scripts = soup.find_all('script', type='application/ld+json')
    for script in scripts:
        try:
            data = json.loads(script.string)
            structured_data.append(data)
        except json.JSONDecodeError:
            continue
    return structured_data


def save_scraped_data(data, filename=None):
    """Save scraped data to a file"""
    if filename is None:
        # Generate filename based on URL and timestamp
        parsed_url = urlparse(data['url'])
        domain = parsed_url.netloc.replace('.', '_')
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        filename = f"scraped_{domain}_{timestamp}.json"

    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

    print(f"Data saved to: {filename}")
    return filename


def scrape_multiple_websites(urls, output_format='structured'):
    """
    Scrape multiple websites and return combined results

    Args:
        urls (list): List of URLs to scrape
        output_format (str): Format for output

    Returns:
        list: List of scraped data for each URL
    """
    all_scraped_data = []

    print(f"Starting to scrape {len(urls)} websites...")

    for i, url in enumerate(urls, 1):
        print(f"\n[{i}/{len(urls)}] Scraping: {url}")

        try:
            scraped_data = scrape_webpage(url, output_format=output_format)
            all_scraped_data.append(scraped_data)

            if 'error' in scraped_data:
                print(f"❌ Error scraping {url}: {scraped_data['error']}")
            else:
                print(f"✅ Successfully scraped: {scraped_data['title']}")

            # Add a small delay between requests to be respectful
            time.sleep(1)

        except Exception as e:
            error_data = {
                'url': url,
                'error': f"Unexpected error: {str(e)}",
                'scraped_at': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            all_scraped_data.append(error_data)
            print(f"❌ Unexpected error scraping {url}: {str(e)}")

    return all_scraped_data


def create_pdf_report(scraped_data_list, output_filename=None):
    """
    Create a PDF report from scraped data

    Args:
        scraped_data_list (list): List of scraped data dictionaries
        output_filename (str): Output PDF filename

    Returns:
        str: Path to the generated PDF file
    """
    if output_filename is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_filename = f"web_scraping_report_{timestamp}.pdf"

    # Create the PDF document
    doc = SimpleDocTemplate(output_filename, pagesize=A4)
    story = []

    # Get styles
    styles = getSampleStyleSheet()

    # Custom styles
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.darkblue
    )

    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=16,
        spaceAfter=12,
        spaceBefore=20,
        textColor=colors.darkgreen
    )

    subheading_style = ParagraphStyle(
        'CustomSubHeading',
        parent=styles['Heading3'],
        fontSize=14,
        spaceAfter=8,
        spaceBefore=12,
        textColor=colors.darkred
    )

    normal_style = ParagraphStyle(
        'CustomNormal',
        parent=styles['Normal'],
        fontSize=10,
        spaceAfter=6,
        alignment=TA_JUSTIFY
    )

    # Title page
    story.append(Paragraph("Web Scraping Report", title_style))
    story.append(Spacer(1, 20))

    # Summary information
    total_sites = len(scraped_data_list)
    successful_sites = len([data for data in scraped_data_list if 'error' not in data])
    failed_sites = total_sites - successful_sites

    summary_text = f"""
    <b>Report Summary:</b><br/>
    • Total websites scraped: {total_sites}<br/>
    • Successfully scraped: {successful_sites}<br/>
    • Failed to scrape: {failed_sites}<br/>
    • Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}<br/>
    """

    story.append(Paragraph(summary_text, normal_style))
    story.append(Spacer(1, 30))

    # Process each scraped website
    for i, data in enumerate(scraped_data_list, 1):
        # Website header
        story.append(Paragraph(f"Website {i}: {data.get('title', 'Unknown Title')}", heading_style))

        # Basic information
        url_text = f"<b>URL:</b> {data['url']}"
        story.append(Paragraph(url_text, normal_style))

        if 'scraped_at' in data:
            time_text = f"<b>Scraped at:</b> {data['scraped_at']}"
            story.append(Paragraph(time_text, normal_style))

        if 'status_code' in data:
            status_text = f"<b>Status Code:</b> {data['status_code']}"
            story.append(Paragraph(status_text, normal_style))

        story.append(Spacer(1, 12))

        # Handle errors
        if 'error' in data:
            error_text = f"<b>Error:</b> {data['error']}"
            story.append(Paragraph(error_text, normal_style))
            story.append(PageBreak())
            continue

        # Content based on format
        content = data.get('content', {})

        if isinstance(content, dict):
            # Structured content
            add_structured_content_to_pdf(story, content, subheading_style, normal_style)
        else:
            # Text content
            story.append(Paragraph("Content:", subheading_style))
            # Limit content length for PDF
            text_content = str(content)[:5000]
            if len(str(content)) > 5000:
                text_content += "... (content truncated)"

            story.append(Paragraph(text_content, normal_style))

        # Add page break between websites
        if i < len(scraped_data_list):
            story.append(PageBreak())

    # Build the PDF
    doc.build(story)
    print(f"PDF report generated: {output_filename}")
    return output_filename


def add_structured_content_to_pdf(story, content, subheading_style, normal_style):
    """Add structured content to PDF story"""

    # Headings
    headings = content.get('headings', [])
    if headings:
        story.append(Paragraph("Headings:", subheading_style))
        for heading in headings[:10]:  # Limit to first 10 headings
            heading_text = f"H{heading['level']}: {heading['text']}"
            story.append(Paragraph(heading_text, normal_style))
        story.append(Spacer(1, 12))

    # Paragraphs
    paragraphs = content.get('paragraphs', [])
    if paragraphs:
        story.append(Paragraph("Content Paragraphs:", subheading_style))
        for para in paragraphs[:5]:  # Limit to first 5 paragraphs
            # Truncate long paragraphs
            para_text = para[:500] + "..." if len(para) > 500 else para
            story.append(Paragraph(para_text, normal_style))
        story.append(Spacer(1, 12))

    # Links
    links = content.get('links', [])
    if links:
        story.append(Paragraph("Links Found:", subheading_style))
        for link in links[:10]:  # Limit to first 10 links
            link_text = f"• {link['text']}: {link['url']}"
            story.append(Paragraph(link_text, normal_style))
        story.append(Spacer(1, 12))

    # Images
    images = content.get('images', [])
    if images:
        story.append(Paragraph("Images Found:", subheading_style))
        for img in images[:5]:  # Limit to first 5 images
            img_text = f"• {img['alt']}: {img['src']}"
            story.append(Paragraph(img_text, normal_style))
        story.append(Spacer(1, 12))

    # Tables
    tables = content.get('tables', [])
    if tables:
        story.append(Paragraph("Tables Found:", subheading_style))
        story.append(Paragraph(f"Number of tables: {len(tables)}", normal_style))
        story.append(Spacer(1, 12))


# Main execution
if __name__ == "__main__":
    print("=== Multi-Website Web Scraping with PDF Generation ===")

    # List of URLs to scrape - ADD YOUR URLS HERE
    urls_to_scrape = [
        "https://onecloud.rakuten-it.com/one-docs/docs/Data/PipelineService/pipeline-service-page",
        # Add more URLs here as needed
        # "https://example.com/page1",
        # "https://example.com/page2",
        # "https://another-site.com/documentation",
    ]

    print(f"URLs to scrape: {len(urls_to_scrape)}")
    for i, url in enumerate(urls_to_scrape, 1):
        print(f"{i}. {url}")

    # Option 1: Scrape multiple websites and generate PDF
    print("\n=== Starting Multi-Website Scraping ===")
    all_scraped_data = scrape_multiple_websites(urls_to_scrape, output_format='structured')

    # Save individual JSON files
    print("\n=== Saving Individual JSON Files ===")
    for i, data in enumerate(all_scraped_data):
        if 'error' not in data:
            filename = f"scraped_site_{i+1}.json"
            save_scraped_data(data, filename)

    # Generate combined PDF report
    print("\n=== Generating PDF Report ===")
    pdf_filename = create_pdf_report(all_scraped_data)

    # Generate summary
    successful_scrapes = [data for data in all_scraped_data if 'error' not in data]
    failed_scrapes = [data for data in all_scraped_data if 'error' in data]

    print(f"\n=== Scraping Summary ===")
    print(f"✅ Successfully scraped: {len(successful_scrapes)} websites")
    print(f"❌ Failed to scrape: {len(failed_scrapes)} websites")
    print(f"📄 PDF report generated: {pdf_filename}")

    if successful_scrapes:
        print(f"\n=== Successfully Scraped Websites ===")
        for i, data in enumerate(successful_scrapes, 1):
            print(f"{i}. {data['title']} - {data['url']}")

    if failed_scrapes:
        print(f"\n=== Failed Websites ===")
        for i, data in enumerate(failed_scrapes, 1):
            print(f"{i}. {data['url']} - Error: {data['error']}")

    print("\n=== Web Scraping Completed ===")


def scrape_custom_urls():
    """
    Function to scrape custom URLs - call this function with your own URL list
    """
    # Example usage for custom URLs
    custom_urls = [
        "https://httpbin.org/html",
        "https://quotes.toscrape.com/",
        "https://books.toscrape.com/",
    ]

    print("=== Custom URL Scraping ===")
    scraped_data = scrape_multiple_websites(custom_urls, output_format='structured')
    pdf_file = create_pdf_report(scraped_data, "custom_scraping_report.pdf")

    return scraped_data, pdf_file
