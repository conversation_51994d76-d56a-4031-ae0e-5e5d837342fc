<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="29">
            <item index="0" class="java.lang.String" itemvalue="pyarrow" />
            <item index="1" class="java.lang.String" itemvalue="dnspython" />
            <item index="2" class="java.lang.String" itemvalue="pymongo" />
            <item index="3" class="java.lang.String" itemvalue="six" />
            <item index="4" class="java.lang.String" itemvalue="cffi" />
            <item index="5" class="java.lang.String" itemvalue="oracledb" />
            <item index="6" class="java.lang.String" itemvalue="cryptography" />
            <item index="7" class="java.lang.String" itemvalue="requests" />
            <item index="8" class="java.lang.String" itemvalue="numpy" />
            <item index="9" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="10" class="java.lang.String" itemvalue="mysql-connector-python" />
            <item index="11" class="java.lang.String" itemvalue="pandas" />
            <item index="12" class="java.lang.String" itemvalue="certifi" />
            <item index="13" class="java.lang.String" itemvalue="cachetools" />
            <item index="14" class="java.lang.String" itemvalue="pytz" />
            <item index="15" class="java.lang.String" itemvalue="urllib3" />
            <item index="16" class="java.lang.String" itemvalue="pyparsing" />
            <item index="17" class="java.lang.String" itemvalue="libcst" />
            <item index="18" class="java.lang.String" itemvalue="db-dtypes" />
            <item index="19" class="java.lang.String" itemvalue="trino" />
            <item index="20" class="java.lang.String" itemvalue="asyncio" />
            <item index="21" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="22" class="java.lang.String" itemvalue="psycopg2-binary" />
            <item index="23" class="java.lang.String" itemvalue="aiohttp" />
            <item index="24" class="java.lang.String" itemvalue="google" />
            <item index="25" class="java.lang.String" itemvalue="bigquery" />
            <item index="26" class="java.lang.String" itemvalue="pycryptodome" />
            <item index="27" class="java.lang.String" itemvalue="Jinja2" />
            <item index="28" class="java.lang.String" itemvalue="pip" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>