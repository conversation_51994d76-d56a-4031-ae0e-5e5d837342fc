#!/usr/bin/env python3
"""
Configuration-based Multi-URL Web Scraper
Reads URLs from urls_config.json and generates PDF reports
"""

import json
import os
from Web import scrape_multiple_websites, create_pdf_report
from datetime import datetime

def load_config(config_file="urls_config.json"):
    """Load configuration from JSON file"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config
    except FileNotFoundError:
        print(f"❌ Configuration file '{config_file}' not found!")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ Error parsing configuration file: {e}")
        return None

def scrape_from_config(config_file="urls_config.json", group_name=None):
    """
    Scrape websites based on configuration file
    
    Args:
        config_file (str): Path to configuration JSON file
        group_name (str): Specific group to scrape (optional)
    """
    # Load configuration
    config = load_config(config_file)
    if not config:
        return None, None
    
    website_groups = config.get('website_groups', {})
    settings = config.get('scraping_settings', {})
    
    # Determine which groups to scrape
    if group_name:
        if group_name not in website_groups:
            print(f"❌ Group '{group_name}' not found in configuration!")
            print(f"Available groups: {list(website_groups.keys())}")
            return None, None
        groups_to_scrape = {group_name: website_groups[group_name]}
    else:
        groups_to_scrape = website_groups
    
    # Collect all URLs
    all_urls = []
    group_info = []
    
    for group, data in groups_to_scrape.items():
        urls = data.get('urls', [])
        description = data.get('description', 'No description')
        
        print(f"📂 Group: {group}")
        print(f"   Description: {description}")
        print(f"   URLs: {len(urls)}")
        
        all_urls.extend(urls)
        group_info.append({
            'name': group,
            'description': description,
            'url_count': len(urls),
            'urls': urls
        })
    
    if not all_urls:
        print("❌ No URLs found to scrape!")
        return None, None
    
    print(f"\n🚀 Starting scraping of {len(all_urls)} URLs from {len(groups_to_scrape)} groups")
    print("-" * 60)
    
    # Scrape all URLs
    output_format = settings.get('output_format', 'structured')
    scraped_data = scrape_multiple_websites(all_urls, output_format=output_format)
    
    # Generate timestamp for filenames
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Save combined JSON if requested
    if settings.get('save_individual_json', True):
        json_filename = f"scraped_data_{timestamp}.json"
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump({
                'scraping_info': {
                    'timestamp': timestamp,
                    'total_urls': len(all_urls),
                    'groups_scraped': group_info,
                    'settings': settings
                },
                'scraped_data': scraped_data
            }, f, indent=2, ensure_ascii=False)
        print(f"💾 Combined data saved to: {json_filename}")
    
    # Generate PDF if requested
    pdf_filename = None
    if settings.get('generate_pdf', True):
        pdf_filename = f"scraping_report_{timestamp}.pdf"
        create_pdf_report(scraped_data, pdf_filename)
    
    # Print summary
    successful = [data for data in scraped_data if 'error' not in data]
    failed = [data for data in scraped_data if 'error' in data]
    
    print("\n" + "="*60)
    print("📊 SCRAPING SUMMARY")
    print("="*60)
    print(f"✅ Successfully scraped: {len(successful)} websites")
    print(f"❌ Failed to scrape: {len(failed)} websites")
    if pdf_filename:
        print(f"📄 PDF report: {pdf_filename}")
    
    # Group-wise summary
    print(f"\n📂 GROUP SUMMARY:")
    for group in group_info:
        group_urls = group['urls']
        group_successful = sum(1 for data in scraped_data 
                             if data['url'] in group_urls and 'error' not in data)
        print(f"   {group['name']}: {group_successful}/{group['url_count']} successful")
    
    return scraped_data, pdf_filename

def list_available_groups(config_file="urls_config.json"):
    """List all available groups in the configuration"""
    config = load_config(config_file)
    if not config:
        return
    
    website_groups = config.get('website_groups', {})
    
    print("📋 Available Website Groups:")
    print("-" * 40)
    for group_name, group_data in website_groups.items():
        description = group_data.get('description', 'No description')
        url_count = len(group_data.get('urls', []))
        print(f"🔹 {group_name}")
        print(f"   Description: {description}")
        print(f"   URLs: {url_count}")
        print()

def main():
    """Main function with interactive options"""
    print("🌐 Configuration-based Web Scraper")
    print("="*50)
    
    # Check if config file exists
    if not os.path.exists("urls_config.json"):
        print("❌ Configuration file 'urls_config.json' not found!")
        print("Please create the configuration file first.")
        return
    
    # List available groups
    list_available_groups()
    
    # Ask user what to scrape
    print("Options:")
    print("1. Scrape all groups")
    print("2. Scrape specific group")
    print("3. Exit")
    
    choice = input("\nEnter your choice (1-3): ").strip()
    
    if choice == "1":
        print("\n🚀 Scraping all groups...")
        scraped_data, pdf_file = scrape_from_config()
        
    elif choice == "2":
        group_name = input("Enter group name to scrape: ").strip()
        print(f"\n🎯 Scraping group: {group_name}")
        scraped_data, pdf_file = scrape_from_config(group_name=group_name)
        
    elif choice == "3":
        print("👋 Goodbye!")
        return
        
    else:
        print("❌ Invalid choice!")
        return
    
    if pdf_file:
        print(f"\n🎉 Scraping completed!")
        print(f"📖 Open '{pdf_file}' to view the report")

if __name__ == "__main__":
    main()
